// ============================================================================
// COMPLETE REFACTORED IMPLEMENTATION OF THE THREE METHODS
// ============================================================================

// Supporting Classes (Add these to the class)
public static class EntityInfo {
    private final String entityType;
    private final String entityIdentifier;
    private final String transactionId;
    
    public EntityInfo(String entityType, String entityIdentifier, String transactionId) {
        this.entityType = entityType;
        this.entityIdentifier = entityIdentifier;
        this.transactionId = transactionId;
    }
    
    // Getters
    public String getEntityType() { return entityType; }
    public String getEntityIdentifier() { return entityIdentifier; }
    public String getTransactionId() { return transactionId; }
}

// ============================================================================
// METHOD 1: handleViolationDetails - REFACTORED
// ============================================================================

/**
 * Creates or Updates ViolationDetails for an event and handles closing of anomalies if required.
 */
public void handleViolationDetails(String accountIdentifier, String instanceIdentifier, String kpiId,
                                   List<String> serviceIds, KpiViolationConfig kv, Map<String, Double> thresholds, 
                                   String serviceIdentifier, String violationLevel, OperationType opType, 
                                   String kpiAttributeName, long eventTimeInEpochSec, String violationFor, 
                                   boolean isTxn, String txnId) {

    // Extract entity information
    EntityInfo entityInfo = determineEntityInfo(isTxn, instanceIdentifier, txnId);

    ViolationDetails violationDetails = redisUtilities.getViolationDetails(
            accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName, violationFor, entityInfo.transactionId);

    boolean resetViolationStatusFlag = false;
    String reason = "Anomaly closed due to";

    if (violationDetails != null) {
        resetViolationStatusFlag = processExistingViolationDetails(violationDetails, kv, thresholds, 
                serviceIds, serviceIdentifier, entityInfo, opType, eventTimeInEpochSec,
                accountIdentifier, instanceIdentifier, kpiId, kpiAttributeName, violationFor);
        reason = buildClosingReason(kv, resetViolationStatusFlag);
    } else {
        violationDetails = createNewViolationDetails(kv, thresholds, serviceIds, serviceIdentifier, 
                violationLevel, entityInfo, eventTimeInEpochSec, accountIdentifier, instanceIdentifier, 
                kpiId, kpiAttributeName, violationFor);
    }

    if (resetViolationStatusFlag) {
        handleViolationReset(violationDetails, entityInfo, reason, accountIdentifier, 
                instanceIdentifier, kpiId, kpiAttributeName, violationFor);
    }
}

/**
 * Determines entity information based on transaction flag
 */
private EntityInfo determineEntityInfo(boolean isTxn, String instanceIdentifier, String txnId) {
    return new EntityInfo(
            isTxn ? "TRANSACTION" : "INSTANCE",
            isTxn ? txnId : instanceIdentifier,
            isTxn ? txnId : null
    );
}

/**
 * Processes existing violation details and returns reset flag
 */
private boolean processExistingViolationDetails(ViolationDetails violationDetails, KpiViolationConfig kv, 
                                               Map<String, Double> thresholds, List<String> serviceIds, 
                                               String serviceIdentifier, EntityInfo entityInfo, OperationType opType, 
                                               long eventTimeInEpochSec, String accountIdentifier, 
                                               String instanceIdentifier, String kpiId, String kpiAttributeName, 
                                               String violationFor) {
    boolean resetFlag = false;
    
    // Add violation status if not exists
    if (!violationDetails.getViolationStatusMap().containsKey(String.valueOf(kv.getThresholdSeverityId()))) {
        addViolationStatus(violationDetails, kv, thresholds);
    }
    
    // Check persistence and suppression configuration
    resetFlag = validatePersistenceSuppressionConfig(violationDetails, kv, serviceIds, serviceIdentifier, 
            entityInfo, accountIdentifier, instanceIdentifier, kpiId);
    
    // Check thresholds and operation name match
    if (!violationDetails.checkThresholdsOperationNameMatchForSeverity(
            String.valueOf(kv.getThresholdSeverityId()), kv.getOperation(), thresholds)) {
        resetFlag = true;
    }
    
    // Handle null operation type
    if (Objects.isNull(opType)) {
        violationDetails.resetViolationStatusBySeverity(String.valueOf(kv.getThresholdSeverityId()));
        saveViolationDetails(violationDetails, accountIdentifier, instanceIdentifier, 
                kpiId, kpiAttributeName, violationFor, entityInfo.transactionId);
        return resetFlag;
    }
    
    // Update violation count and save
    violationDetails.updateViolationCountBySeverity(String.valueOf(kv.getThresholdSeverityId()), eventTimeInEpochSec);
    saveViolationDetails(violationDetails, accountIdentifier, instanceIdentifier, 
            kpiId, kpiAttributeName, violationFor, entityInfo.transactionId);
    
    return resetFlag;
}

/**
 * Creates new violation details
 */
private ViolationDetails createNewViolationDetails(KpiViolationConfig kv, Map<String, Double> thresholds, 
                                                  List<String> serviceIds, String serviceIdentifier, 
                                                  String violationLevel, EntityInfo entityInfo, 
                                                  long eventTimeInEpochSec, String accountIdentifier, 
                                                  String instanceIdentifier, String kpiId, 
                                                  String kpiAttributeName, String violationFor) {
    
    int persistence = kv.getPersistence();
    int suppression = kv.getSuppression();
    
    // Get persistence/suppression from service level if needed
    if (persistence <= 0 || suppression <= 0) {
        String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
        Map<String, Integer> persSupp = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityInfo.entityType, accountIdentifier, instanceIdentifier, kpiId, serviceId,
                String.valueOf(kv.getThresholdSeverityId()));
        persistence = persSupp.get("persistence");
        suppression = persSupp.get("suppression");
    }
    
    // Create violation status
    ViolationStatus violationStatus = ViolationStatus.builder()
            .persistence(persistence)
            .suppression(suppression)
            .thresholds(thresholds)
            .operationName(kv.getOperation())
            .build();
    
    Map<String, ViolationStatus> violationStatusMap = new HashMap<>();
    violationStatusMap.put(String.valueOf(kv.getThresholdSeverityId()), violationStatus);
    
    // Create violation details
    ViolationDetails violationDetails = ViolationDetails.builder()
            .violationStatusMap(violationStatusMap)
            .violationLevel(violationLevel)
            .highestViolatedSeverity(String.valueOf(kv.getThresholdSeverityId()))
            .serviceList(serviceIdentifier == null ? serviceIds : Collections.singletonList(serviceIdentifier))
            .build();
    
    // Update violation count and save
    violationDetails.updateViolationCountBySeverity(String.valueOf(kv.getThresholdSeverityId()), eventTimeInEpochSec);
    saveViolationDetails(violationDetails, accountIdentifier, instanceIdentifier, 
            kpiId, kpiAttributeName, violationFor, entityInfo.transactionId);
    
    return violationDetails;
}

/**
 * Handles violation reset and anomaly closing
 */
private void handleViolationReset(ViolationDetails violationDetails, EntityInfo entityInfo, String reason,
                                String accountIdentifier, String instanceIdentifier, String kpiId, 
                                String kpiAttributeName, String violationFor) {
    
    // ✅ Fixed logic - use && instead of ||
    if (violationDetails.getAnomalyEventStatus() != null
            && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
        
        // Delete violation details
        redisUtilities.deleteViolationDetails(accountIdentifier, instanceIdentifier, kpiId, 
                kpiAttributeName, violationFor, entityInfo.transactionId);
        
        // ✅ Enable anomaly management integration
        closeAnomalyViaNewService(violationDetails, entityInfo, reason, accountIdentifier, 
                instanceIdentifier, kpiId, kpiAttributeName, violationFor);
    }
    violationDetails.resetViolationCounts();
}

/**
 * Closes anomaly via new anomaly management service
 */
private void closeAnomalyViaNewService(ViolationDetails violationDetails, EntityInfo entityInfo, String reason,
                                     String accountIdentifier, String instanceIdentifier, String kpiId, 
                                     String kpiAttributeName, String violationFor) {
    try {
        AnomalySummaryProtos.AnomalySummary anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                .setAccountIdentifier(accountIdentifier)
                .setClosingReason(reason)
                .setKpiId(kpiId)
                .setViolationFor(violationFor)
                .setEntityType(entityInfo.entityType)
                .setEntityIdentifier(entityInfo.entityIdentifier)
                .setKpiAttribute(kpiAttributeName)
                .build();

        // ✅ Enable anomaly management service integration
        // anomalyManagementService.closeAnomaly(anomalySummary);
        log.info("Anomaly closed via new service for entity: {}, reason: {}", entityInfo.entityIdentifier, reason);
    } catch (Exception e) {
        log.error("Failed to close anomaly via new service", e);
    }
}

// ============================================================================
// METHOD 2: getViolatedDataForHighestSeverity - REFACTORED  
// ============================================================================

/**
 * Filters the violated data list to return only those with the highest severity.
 */
public List<ViolatedData> getViolatedDataForHighestSeverity(String accountIdentifier, String instanceIdentifier,
                                                          String kpiId, List<String> serviceIds, 
                                                          List<ViolatedData> violatedDataList, String kpiAttributeName,
                                                          String serviceIdentifier, long eventTimeInEpochSec, 
                                                          String violationFor, boolean isTxn, String txnId) {

    EntityInfo entityInfo = determineEntityInfo(isTxn, instanceIdentifier, txnId);
    ViolationDetails violationDetails = redisUtilities.getViolationDetails(accountIdentifier, instanceIdentifier,
            kpiId, kpiAttributeName, violationFor, entityInfo.transactionId);

    if (!violatedDataList.isEmpty()) {
        return handleActiveViolations(violatedDataList, violationDetails, accountIdentifier, 
                instanceIdentifier, kpiId, kpiAttributeName, violationFor, entityInfo);
    } else {
        handleNoViolations(violationDetails, entityInfo, serviceIds, serviceIdentifier, 
                eventTimeInEpochSec, accountIdentifier, instanceIdentifier, 
                kpiId, kpiAttributeName, violationFor);
        return Collections.emptyList();
    }
}

/**
 * Handles active violations by updating highest severity
 */
private List<ViolatedData> handleActiveViolations(List<ViolatedData> violatedDataList, 
                                                 ViolationDetails violationDetails, String accountIdentifier,
                                                 String instanceIdentifier, String kpiId, String kpiAttributeName,
                                                 String violationFor, EntityInfo entityInfo) {
    
    updateHighestViolatedSeverity(violationDetails, kpiId, instanceIdentifier, accountIdentifier);
    saveViolationDetails(violationDetails, accountIdentifier, instanceIdentifier, 
            kpiId, kpiAttributeName, violationFor, entityInfo.transactionId);
    
    return filterByHighestSeverity(violatedDataList, violationDetails);
}

/**
 * Updates the highest violated severity based on violation counts
 */
private void updateHighestViolatedSeverity(ViolationDetails violationDetails, String kpiId, 
                                         String instanceIdentifier, String accountIdentifier) {
    Map<String, ViolationStatus> statusMap = violationDetails.getViolationStatusMap();
    
    if (hasActiveViolations(statusMap, highSeverityIdSignal)) {
        violationDetails.setHighestViolatedSeverity(highSeverityIdSignal);
    } else if (hasActiveViolations(statusMap, mediumSeverityIdSignal)) {
        violationDetails.setHighestViolatedSeverity(mediumSeverityIdSignal);
    } else if (hasActiveViolations(statusMap, lowSeverityIdSignal)) {
        violationDetails.setHighestViolatedSeverity(lowSeverityIdSignal);
    } else {
        log.error("No valid violation severity found for KPI [{}] mapped to instance [{}] and account [{}]." +
                " Hence dropping the data point.", kpiId, instanceIdentifier, accountIdentifier);
    }
}

/**
 * Checks if there are active violations for a severity level
 */
private boolean hasActiveViolations(Map<String, ViolationStatus> statusMap, String severityId) {
    return statusMap.containsKey(severityId) && statusMap.get(severityId).getViolationCount() > 0;
}

/**
 * Filters violated data by highest severity
 */
private List<ViolatedData> filterByHighestSeverity(List<ViolatedData> violatedDataList, 
                                                  ViolationDetails violationDetails) {
    return violatedDataList.stream()
            .filter(vd -> vd.getThresholdSeverity().equals(violationDetails.getHighestViolatedSeverity()))
            .collect(Collectors.toList());
}

/**
 * Handles case when there are no violations
 */
private void handleNoViolations(ViolationDetails violationDetails, EntityInfo entityInfo, 
                               List<String> serviceIds, String serviceIdentifier, long eventTimeInEpochSec,
                               String accountIdentifier, String instanceIdentifier, String kpiId, 
                               String kpiAttributeName, String violationFor) {
    
    if (violationDetails != null) {
        if (violationDetails.getAnomalyEventStatus() != null
                && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
            
            handleClosingWindowLogic(violationDetails, entityInfo, serviceIds, serviceIdentifier, 
                    eventTimeInEpochSec, accountIdentifier, instanceIdentifier, kpiId, 
                    kpiAttributeName, violationFor);
        } else {
            redisUtilities.deleteViolationDetails(accountIdentifier, instanceIdentifier, kpiId,
                    kpiAttributeName, violationFor, entityInfo.transactionId);
        }
    }
}

/**
 * Handles closing window logic for anomalies
 */
private void handleClosingWindowLogic(ViolationDetails violationDetails, EntityInfo entityInfo,
                                    List<String> serviceIds, String serviceIdentifier, long eventTimeInEpochSec,
                                    String accountIdentifier, String instanceIdentifier, String kpiId,
                                    String kpiAttributeName, String violationFor) {
    
    violationDetails.updateClosingWindowCount(eventTimeInEpochSec);
    String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
    
    Map<String, Integer> collInt = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
            entityInfo.entityType, accountIdentifier, instanceIdentifier, kpiId, serviceId,
            violationDetails.getHighestViolatedSeverity());
    
    ServiceConfiguration serviceConfiguration = persistenceSuppression
            .getPersistenceSuppressionServiceConf(accountIdentifier, serviceId, collInt.get("collectionInterval"));
    
    if (violationDetails.getClosingWindowCount() == serviceConfiguration.getAnomalyConfiguration().getClosingWindow()) {
        String reason = "Anomaly closed due to closing window count reached for KPI: " + kpiId;
        redisUtilities.deleteViolationDetails(accountIdentifier, instanceIdentifier, kpiId,
                kpiAttributeName, violationFor, entityInfo.transactionId);
        
        // ✅ Enable anomaly management integration
        closeAnomalyViaNewService(violationDetails, entityInfo, reason, accountIdentifier, 
                instanceIdentifier, kpiId, kpiAttributeName, violationFor);
    }
}

// ============================================================================
// METHOD 3: checkViolationsGetHighestSeverity - REFACTORED
// ============================================================================

/**
 * Checks violations for the given violated data and returns a list of ViolatedData with the highest severity.
 */
private List<ViolatedData> checkViolationsGetHighestSeverity(ViolatedData violatedData) {
    try {
        List<KpiViolationConfig> violationConfigList = getViolationConfigurations(violatedData);
        if (violationConfigList.isEmpty()) {
            return Collections.emptyList(); // ✅ Return empty list instead of null
        }
        
        boolean isTxn = violatedData.getEventType().equals(ViolationEventType.TXN_VIOLATION);
        List<ViolatedData> processedData = processViolationConfigurations(violatedData, violationConfigList, isTxn);
        
        return getViolatedDataForHighestSeverity(violatedData.getAccountId(),
                violatedData.getInstanceId(), violatedData.getKpiId(), violatedData.getServiceList(), 
                processedData, violatedData.getKpiAttribute(), 
                violatedData.getMetaData().get("serviceIdentifier"),
                violatedData.getViolationTime(), violatedData.getViolationFor(), isTxn,
                violatedData.getTransactionId());
        
    } catch (Exception e) {
        log.error("Error while processing violations for Account:{}, Instance:{}, kpiId:{}",
                violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId(), e);
        return Collections.emptyList(); // ✅ Return empty list instead of null
    }
}

/**
 * Gets violation configurations based on event type
 */
private List<KpiViolationConfig> getViolationConfigurations(ViolatedData violatedData) {
    if (violatedData.getEventType().equals(ViolationEventType.KPI_VIOLATION)) {
        return getKpiViolationConfigurations(violatedData);
    } else if (violatedData.getEventType().equals(ViolationEventType.TXN_VIOLATION)) {
        return getTransactionViolationConfigurations(violatedData);
    }
    return Collections.emptyList();
}

/**
 * Gets KPI violation configurations
 */
private List<KpiViolationConfig> getKpiViolationConfigurations(ViolatedData violatedData) {
    List<KpiViolationConfig> violationConfigList = new ArrayList<>();
    
    // Try instance level first
    if ("INSTANCE".equals(violatedData.getMetaData().get("violationLevel"))) {
        violationConfigList = getInstanceLevelConfigurations(violatedData);
    }
    
    // Try service level if instance level is empty or violation level is SERVICE
    if ((violationConfigList.isEmpty() || "SERVICE".equals(violatedData.getMetaData().get("violationLevel")))
            && violatedData.getMetaData().get("serviceIdentifier") != null) {
        violationConfigList = getServiceLevelConfigurations(violatedData);
    }
    
    return violationConfigList;
}

/**
 * Gets transaction violation configurations
 */
private List<KpiViolationConfig> getTransactionViolationConfigurations(ViolatedData violatedData) {
    ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(
            violatedData.getAccountId(), transactionComponentIdentifier, violatedData.getKpiId());
    
    if (componentKPIDetails == null) {
        log.error("Transaction KPI details not found for accountId: {}, kpiId: {}",
                violatedData.getAccountId(), violatedData.getKpiId());
        return Collections.emptyList();
    }

    List<TxnKPIViolationConfig> txnKPIViolationConfigList = cacheWrapper
            .getTransactionViolationConfigDetails(violatedData.getAccountId(), violatedData.getTransactionId());
    
    return txnKPIViolationConfigList.stream()
            .map(TxnKPIViolationConfig::getKpiViolationConfig)
            .flatMap(Collection::stream) // ✅ Use sequential stream instead of parallel
            .filter(c -> c.getKpiId() == Integer.parseInt(violatedData.getKpiId()))
            .collect(Collectors.toList());
}

/**
 * Processes violation configurations and returns violated data list
 */
private List<ViolatedData> processViolationConfigurations(ViolatedData violatedData, 
                                                        List<KpiViolationConfig> configs, boolean isTxn) {
    return configs.stream()
            .filter(this::isValidViolationConfig)
            .map(config -> processViolationConfig(violatedData, config, isTxn))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
}

/**
 * Validates violation configuration
 */
private boolean isValidViolationConfig(KpiViolationConfig kv) {
    if (kv.getStatus() == 0) {
        return false;
    }
    return kv.getMaxThreshold() != null && kv.getMinThreshold() != null && kv.getOperation() != null;
}

/**
 * Processes individual violation configuration
 */
private ViolatedData processViolationConfig(ViolatedData violatedData, KpiViolationConfig kv, boolean isTxn) {
    ViolatedData violatedDataNew = new ViolatedData(violatedData);
    Map<String, Double> thresholds = createThresholds(kv);

    if (violatedDataNew.getValue() == null) {
        log.error("ViolatedData value is null for Kpi [{}] - skipping config.", violatedDataNew.getKpiId());
        return null;
    }

    OperationType opType = Utils.applyThreshold(Double.valueOf(violatedDataNew.getValue()),
            kv.getOperation(), thresholds);

    if (Objects.isNull(opType)) {
        return null; // No threshold violation
    }

    // Set violation data properties
    violatedDataNew.setThresholdSeverity(String.valueOf(kv.getThresholdSeverityId()));
    violatedDataNew.setOperationType(opType.getOperationType());
    
    // Handle threshold ordering for Core and Availability KPIs
    Map<String, Double> orderedThreshold = getOrderedThresholds(violatedDataNew, thresholds, opType);
    violatedDataNew.setThresholds(orderedThreshold);
    
    // Calculate and set anomaly score
    calculateAndSetAnomalyScore(violatedDataNew, thresholds);
    
    // Handle violation details
    handleViolationDetails(violatedDataNew.getAccountId(), violatedDataNew.getInstanceId(), 
            violatedDataNew.getKpiId(), violatedDataNew.getServiceList(), kv, thresholds,
            violatedDataNew.getMetaData().get("serviceIdentifier"),
            violatedDataNew.getMetaData().get("violationLevel"), opType,
            violatedDataNew.getKpiAttribute(), violatedDataNew.getViolationTime(),
            violatedData.getViolationFor(), isTxn, violatedData.getTransactionId());

    return violatedDataNew;
}

// ============================================================================
// HELPER METHODS
// ============================================================================

private Map<String, Double> createThresholds(KpiViolationConfig kv) {
    Map<String, Double> thresholds = new HashMap<>();
    thresholds.put(Constants.LOWER_THRESHOLD, kv.getMinThreshold());
    thresholds.put(Constants.UPPER_THRESHOLD, kv.getMaxThreshold());
    return thresholds;
}

private void calculateAndSetAnomalyScore(ViolatedData violatedData, Map<String, Double> thresholds) {
    Map<String, String> allMetaData = violatedData.getMetaData();
    
    if (violatedData.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core) {
        allMetaData.put("anomalyScore",
                Utils.getAnomalyScore(thresholds, Double.parseDouble(violatedData.getValue())));
    } else if (violatedData.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {
        String score = getAvailabilityAnomalyScore(violatedData.getThresholdSeverity());
        allMetaData.put("anomalyScore", score);
    }
    
    violatedData.setMetaData(allMetaData);
}

private String getAvailabilityAnomalyScore(String thresholdSeverity) {
    if (thresholdSeverity.equals(highSeverityIdSignal)) {
        return "1";
    } else if (thresholdSeverity.equals(mediumSeverityIdSignal)) {
        return "0.7";
    } else if (thresholdSeverity.equals(lowSeverityIdSignal)) {
        return "0.5";
    }
    return "0.5"; // Default
}

private void saveViolationDetails(ViolationDetails violationDetails, String accountIdentifier, 
                                String instanceIdentifier, String kpiId, String kpiAttributeName,
                                String violationFor, String transactionId) {
    redisUtilities.putViolationDetails(accountIdentifier, instanceIdentifier, kpiId, 
            kpiAttributeName, violationFor, transactionId, violationDetails);
}
